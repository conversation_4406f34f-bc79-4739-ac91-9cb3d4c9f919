<script lang="ts">
  import { WebsiteName } from "$lib/config"
</script>

<!-- Footer -->
<footer class="border-t-2 border-border bg-foreground">
  <div class="max-w-6xl mx-auto px-6 py-12">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <div class="space-y-4">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-background shadow-brutal-sm">
            <span class="font-bold text-sm">R</span>
          </div>
          <h3 class="font-black text-background">{WebsiteName}</h3>
        </div>
        <p class="text-sm text-muted font-medium">
          AI-powered marketing specialists that grow your business.
        </p>
      </div>
      <div class="space-y-4">
        <h4 class="font-bold text-background">Product</h4>
        <ul class="space-y-2 text-sm">
          <li><a href="/" class="text-muted hover:text-background transition-colors font-medium">Overview</a></li>
          <li><a href="/blog" class="text-muted hover:text-background transition-colors font-medium">Blog</a></li>
        </ul>
      </div>
      <div class="space-y-4">
        <h4 class="font-bold text-background">Support</h4>
        <ul class="space-y-2 text-sm">
          <li><a href="/contact_us" class="text-muted hover:text-background transition-colors font-medium">Contact Us</a></li>
          <li><a href="/login" class="text-muted hover:text-background transition-colors font-medium">Sign In</a></li>
        </ul>
      </div>
      <div class="space-y-4">
        <h4 class="font-bold text-background">Connect</h4>
        <div class="flex gap-4">
          <a href="https://github.com/madhukarkumar/saas-starter" aria-label="GitHub" class="text-muted hover:text-background transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
    <div class="border-t-2 border-background mt-8 pt-8 text-center">
      <p class="text-sm text-muted font-medium">
        Copyright © {new Date().getFullYear()} {WebsiteName}. All rights reserved.
      </p>
    </div>
  </div>
</footer>
