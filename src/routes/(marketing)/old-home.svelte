<script lang="ts">
  import { WebsiteName, WebsiteBaseUrl, WebsiteDescription } from "$lib/config"
  import { buttonVariants } from "$lib/components/ui/button"
  import * as Card from "$lib/components/ui/card"
  import { onMount } from "svelte"

  const ldJson = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: WebsiteName,
    url: WebsiteBaseUrl,
  }
  const jsonldScript = `<script type="application/ld+json">${
    JSON.stringify(ldJson) + "<"
  }/script>`

  let chartCanvas: HTMLCanvasElement

  onMount(() => {
    // Initialize chart if Chart.js is available
    if (typeof window !== "undefined" && (window as any).Chart && chartCanvas) {
      const ctx = chartCanvas.getContext("2d")
      new (window as any).Chart(ctx, {
        type: "line",
        data: {
          labels: ["Week 0", "1", "2", "3", "4", "5", "6", "7", "8"],
          datasets: [
            {
              label: "Avg. ROI (%)",
              data: [0, 5, 12, 18, 25, 32, 38, 44, 51],
              borderColor: "#4f46e5",
              backgroundColor: "rgba(99,102,241,.1)",
              tension: 0.4,
              fill: true,
              pointRadius: 3,
              pointBackgroundColor: "#4f46e5",
            },
          ],
        },
        options: {
          responsive: true,
          plugins: { legend: { display: false } },
          scales: {
            y: {
              grid: { color: "rgba(0,0,0,.05)" },
              ticks: { callback: (v: any) => v + "%" },
            },
            x: { grid: { display: false } },
          },
        },
      })
    }
  })

  const features = [
    {
      name: "Audience Analyzer",
      description:
        "Discovers & segments high-intent audiences across channels, enriching profiles with real-time behavioral data.",
      link: "/dashboard",
      linkText: "Live",
      subFeatures: [
        "Look-alike modeling",
        "Churn prediction",
        "Channel scoring",
      ],
      svgContent: `<circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"/><circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="1.5"/><circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="1.5"/>`,
    },
    {
      name: "Content Crafter",
      description:
        "Writes conversion-ready copy, designs creative variations, and A/B tests endlessly to maximize engagement.",
      link: "/dashboard",
      linkText: "Beta",
      subFeatures: [
        "SEO briefs & outlines",
        "Multi-language drafts",
        "Dynamic creative",
      ],
      svgContent: `<path d="M12.67 19a2 2 0 0 0 1.416-.588l6.154-6.172a6 6 0 0 0-8.49-8.49L5.586 9.914A2 2 0 0 0 5 11.328V18a1 1 0 0 0 1 1z" stroke="currentColor" stroke-width="1.5"/><path d="M16 8 2 22" stroke="currentColor" stroke-width="1.5"/><path d="M17.5 15H9" stroke="currentColor" stroke-width="1.5"/>`,
    },
    {
      name: "Campaign Commander",
      description:
        "Launches, budgets, and scales campaigns autonomously—allocating spend to the highest-ROI channels.",
      link: "/dashboard",
      linkText: "Coming soon",
      subFeatures: [
        "Budget rebalancing",
        "ROAS optimization",
        "Channel orchestration",
      ],
      svgContent: `<path d="M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z" stroke="currentColor" stroke-width="1.5"/><path d="M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14" stroke="currentColor" stroke-width="1.5"/><path d="M8 6v8" stroke="currentColor" stroke-width="1.5"/>`,
    },
    {
      name: "User Auth",
      link: "/login",
      description:
        "Sign up, sign out, forgot password, email verification, and oAuth. Powered by Supabase Auth.",
      svgContent: `<path
                  d="M14.2084 13.5521C16.3025 13.5521 18 11.8615 18 9.77606C18 7.6906 16.3025 6 14.2084 6C12.1144 6 10.4169 7.6906 10.4169 9.77606C10.4169 10.742 10.8578 11.4446 10.8578 11.4446L6.27264 16.011C6.0669 16.2159 5.77886 16.7486 6.27264 17.2404L6.8017 17.7673C7.00743 17.9429 7.52471 18.1888 7.94796 17.7673L8.56519 17.1526C9.18242 17.7673 9.88782 17.416 10.1523 17.0647C10.5932 16.45 10.0642 15.8353 10.0642 15.8353L10.2405 15.6597C11.087 16.5027 11.8277 16.011 12.0922 15.6597C12.5331 15.045 12.0922 14.4303 12.0922 14.4303C11.9159 14.079 11.5632 14.079 12.004 13.64L12.5331 13.113C12.9564 13.4643 13.8264 13.5521 14.2084 13.5521Z"
                  stroke="#1C274C"
                  stroke-width="1.5"
                  stroke-linejoin="round"
                />
                <path
                  d="M22 12C22 16.714 22 19.0711 20.5355 20.5355C19.0711 22 16.714 22 12 22C7.28595 22 4.92893 22 3.46447 20.5355C2 19.0711 2 16.714 2 12C2 7.28595 2 4.92893 3.46447 3.46447C4.92893 2 7.28595 2 12 2C16.714 2 19.0711 2 20.5355 3.46447C21.5093 4.43821 21.8356 5.80655 21.9449 8"
                  stroke="#1C274C"
                  stroke-width="1.5"
                  stroke-linecap="round"
                />`,
    },
    {
      name: "Pricing Page",
      link: "/pricing",
      description:
        "Customizable and fast pricing page, integrated into the billing portal.",
      svgContent: `<path d="M12 6V18" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M15 9.5C15 8.11929 13.6569 7 12 7C10.3431 7 9 8.11929 9 9.5C9 10.8807 10.3431 12 12 12C13.6569 12 15 13.1193 15 14.5C15 15.8807 13.6569 17 12 17C10.3431 17 9 15.8807 9 14.5" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M7 3.33782C8.47087 2.48697 10.1786 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 10.1786 2.48697 8.47087 3.33782 7" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Blog",
      link: "/blog",
      description:
        "Blog engine with rich formatting, RSS and SEO optimization.",
      svgContent: `<path d="M3 10C3 6.22876 3 4.34315 4.17157 3.17157C5.34315 2 7.22876 2 11 2H13C16.7712 2 18.6569 2 19.8284 3.17157C21 4.34315 21 6.22876 21 10V14C21 17.7712 21 19.6569 19.8284 20.8284C18.6569 22 16.7712 22 13 22H11C7.22876 22 5.34315 22 4.17157 20.8284C3 19.6569 3 17.7712 3 14V10Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M6 12C6 10.5858 6 9.87868 6.43934 9.43934C6.87868 9 7.58579 9 9 9H15C16.4142 9 17.1213 9 17.5607 9.43934C18 9.87868 18 10.5858 18 12V16C18 17.4142 18 18.1213 17.5607 18.5607C17.1213 19 16.4142 19 15 19H9C7.58579 19 6.87868 19 6.43934 18.5607C6 18.1213 6 17.4142 6 16V12Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M7 6H12" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Subscriptions",
      link: "/pricing",
      description: "User friendly subscriptions powered by Stripe Checkout.",
      svgContent: `<path d="M2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H14C17.7712 4 19.6569 4 20.8284 5.17157C22 6.34315 22 8.22876 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M10 16.5H6" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M8 13.5H6" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M2 10L22 10" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M14 15C14 14.0572 14 13.5858 14.2929 13.2929C14.5858 13 15.0572 13 16 13C16.9428 13 17.4142 13 17.7071 13.2929C18 13.5858 18 14.0572 18 15C18 15.9428 18 16.4142 17.7071 16.7071C17.4142 17 16.9428 17 16 17C15.0572 17 14.5858 17 14.2929 16.7071C14 16.4142 14 15.9428 14 15Z" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Billing Portal",
      link: "/pricing",
      description:
        "Self-serve portal for updating card, receipts, upgrades, downgrades and more.",
      svgContent: `<path d="M7.5 18C8.32843 18 9 18.6716 9 19.5C9 20.3284 8.32843 21 7.5 21C6.67157 21 6 20.3284 6 19.5C6 18.6716 6.67157 18 7.5 18Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M16.5 18.0001C17.3284 18.0001 18 18.6716 18 19.5001C18 20.3285 17.3284 21.0001 16.5 21.0001C15.6716 21.0001 15 20.3285 15 19.5001C15 18.6716 15.6716 18.0001 16.5 18.0001Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M2.26121 3.09184L2.50997 2.38429H2.50997L2.26121 3.09184ZM2.24876 2.29246C1.85799 2.15507 1.42984 2.36048 1.29246 2.75124C1.15507 3.14201 1.36048 3.57016 1.75124 3.70754L2.24876 2.29246ZM4.58584 4.32298L5.20507 3.89983V3.89983L4.58584 4.32298ZM5.88772 14.5862L5.34345 15.1022H5.34345L5.88772 14.5862ZM20.6578 9.88275L21.3923 10.0342L21.3933 10.0296L20.6578 9.88275ZM20.158 12.3075L20.8926 12.4589L20.158 12.3075ZM20.7345 6.69708L20.1401 7.15439L20.7345 6.69708ZM19.1336 15.0504L18.6598 14.469L19.1336 15.0504ZM5.70808 9.76V7.03836H4.20808V9.76H5.70808ZM2.50997 2.38429L2.24876 2.29246L1.75124 3.70754L2.01245 3.79938L2.50997 2.38429ZM10.9375 16.25H16.2404V14.75H10.9375V16.25ZM5.70808 7.03836C5.70808 6.3312 5.7091 5.7411 5.65719 5.26157C5.60346 4.76519 5.48705 4.31247 5.20507 3.89983L3.96661 4.74613C4.05687 4.87822 4.12657 5.05964 4.1659 5.42299C4.20706 5.8032 4.20808 6.29841 4.20808 7.03836H5.70808ZM2.01245 3.79938C2.68006 4.0341 3.11881 4.18965 3.44166 4.34806C3.74488 4.49684 3.87855 4.61727 3.96661 4.74613L5.20507 3.89983C4.92089 3.48397 4.54304 3.21763 4.10241 3.00143C3.68139 2.79485 3.14395 2.60719 2.50997 2.38429L2.01245 3.79938ZM4.20808 9.76C4.20808 11.2125 4.22171 12.2599 4.35876 13.0601C4.50508 13.9144 4.79722 14.5261 5.34345 15.1022L6.43198 14.0702C6.11182 13.7325 5.93913 13.4018 5.83723 12.8069C5.72607 12.1578 5.70808 11.249 5.70808 9.76H4.20808ZM10.9375 14.75C9.52069 14.75 8.53763 14.7482 7.79696 14.6432C7.08215 14.5418 6.70452 14.3576 6.43198 14.0702L5.34345 15.1022C5.93731 15.7286 6.69012 16.0013 7.58636 16.1283C8.45674 16.2518 9.56535 16.25 10.9375 16.25V14.75ZM4.95808 6.87H17.0888V5.37H4.95808V6.87ZM19.9232 9.73135L19.4235 12.1561L20.8926 12.4589L21.3923 10.0342L19.9232 9.73135ZM17.0888 6.87C17.9452 6.87 18.6989 6.871 19.2937 6.93749C19.5893 6.97053 19.8105 7.01643 19.9659 7.07105C20.1273 7.12776 20.153 7.17127 20.1401 7.15439L21.329 6.23978C21.094 5.93436 20.7636 5.76145 20.4632 5.65587C20.1567 5.54818 19.8101 5.48587 19.4604 5.44678C18.7646 5.369 17.9174 5.37 17.0888 5.37V6.87ZM21.3933 10.0296C21.5625 9.18167 21.7062 8.47024 21.7414 7.90038C21.7775 7.31418 21.7108 6.73617 21.329 6.23978L20.1401 7.15439C20.2021 7.23508 20.2706 7.38037 20.2442 7.80797C20.2168 8.25191 20.1002 8.84478 19.9223 9.73595L21.3933 10.0296ZM16.2404 16.25C17.0021 16.25 17.6413 16.2513 18.1566 16.1882C18.6923 16.1227 19.1809 15.9794 19.6074 15.6318L18.6598 14.469C18.5346 14.571 18.3571 14.6525 17.9744 14.6994C17.5712 14.7487 17.0397 14.75 16.2404 14.75V16.25ZM19.4235 12.1561C19.2621 12.9389 19.1535 13.4593 19.0238 13.8442C18.9007 14.2095 18.785 14.367 18.6598 14.469L19.6074 15.6318C20.0339 15.2842 20.2729 14.8346 20.4453 14.3232C20.6111 13.8312 20.7388 13.2049 20.8926 12.4589L19.4235 12.1561Z" fill="#1C274C"/>`,
    },
    {
      name: "User Dashboard",
      link: "/login",
      description:
        "User profile, user settings, update email/password, billing, and more.",
      svgContent: `<circle cx="12" cy="12" r="3" stroke="#1C274C" stroke-width="1.5"/>
<path d="M13.7654 2.15224C13.3978 2 12.9319 2 12 2C11.0681 2 10.6022 2 10.2346 2.15224C9.74457 2.35523 9.35522 2.74458 9.15223 3.23463C9.05957 3.45834 9.0233 3.7185 9.00911 4.09799C8.98826 4.65568 8.70226 5.17189 8.21894 5.45093C7.73564 5.72996 7.14559 5.71954 6.65219 5.45876C6.31645 5.2813 6.07301 5.18262 5.83294 5.15102C5.30704 5.08178 4.77518 5.22429 4.35436 5.5472C4.03874 5.78938 3.80577 6.1929 3.33983 6.99993C2.87389 7.80697 2.64092 8.21048 2.58899 8.60491C2.51976 9.1308 2.66227 9.66266 2.98518 10.0835C3.13256 10.2756 3.3397 10.437 3.66119 10.639C4.1338 10.936 4.43789 11.4419 4.43786 12C4.43783 12.5581 4.13375 13.0639 3.66118 13.3608C3.33965 13.5629 3.13248 13.7244 2.98508 13.9165C2.66217 14.3373 2.51966 14.8691 2.5889 15.395C2.64082 15.7894 2.87379 16.193 3.33973 17C3.80568 17.807 4.03865 18.2106 4.35426 18.4527C4.77508 18.7756 5.30694 18.9181 5.83284 18.8489C6.07289 18.8173 6.31632 18.7186 6.65204 18.5412C7.14547 18.2804 7.73556 18.27 8.2189 18.549C8.70224 18.8281 8.98826 19.3443 9.00911 19.9021C9.02331 20.2815 9.05957 20.5417 9.15223 20.7654C9.35522 21.2554 9.74457 21.6448 10.2346 21.8478C10.6022 22 11.0681 22 12 22C12.9319 22 13.3978 22 13.7654 21.8478C14.2554 21.6448 14.6448 21.2554 14.8477 20.7654C14.9404 20.5417 14.9767 20.2815 14.9909 19.902C15.0117 19.3443 15.2977 18.8281 15.781 18.549C16.2643 18.2699 16.8544 18.2804 17.3479 18.5412C17.6836 18.7186 17.927 18.8172 18.167 18.8488C18.6929 18.9181 19.2248 18.7756 19.6456 18.4527C19.9612 18.2105 20.1942 17.807 20.6601 16.9999C21.1261 16.1929 21.3591 15.7894 21.411 15.395C21.4802 14.8691 21.3377 14.3372 21.0148 13.9164C20.8674 13.7243 20.6602 13.5628 20.3387 13.3608C19.8662 13.0639 19.5621 12.558 19.5621 11.9999C19.5621 11.4418 19.8662 10.9361 20.3387 10.6392C20.6603 10.4371 20.8675 10.2757 21.0149 10.0835C21.3378 9.66273 21.4803 9.13087 21.4111 8.60497C21.3592 8.21055 21.1262 7.80703 20.6602 7C20.1943 6.19297 19.9613 5.78945 19.6457 5.54727C19.2249 5.22436 18.693 5.08185 18.1671 5.15109C17.9271 5.18269 17.6837 5.28136 17.3479 5.4588C16.8545 5.71959 16.2644 5.73002 15.7811 5.45096C15.2977 5.17191 15.0117 4.65566 14.9909 4.09794C14.9767 3.71848 14.9404 3.45833 14.8477 3.23463C14.6448 2.74458 14.2554 2.35523 13.7654 2.15224Z" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Contact Us",
      link: "/contact_us",
      description:
        "Contact form for customers to reach out for demos, quotes, and questions.",
      svgContent: `<path d="M10.5 22V20M14.5 22V20" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11 20V20.75H11.75V20H11ZM14 19.25C13.5858 19.25 13.25 19.5858 13.25 20C13.25 20.4142 13.5858 20.75 14 20.75V19.25ZM17.5 5.25C17.0858 5.25 16.75 5.58579 16.75 6C16.75 6.41421 17.0858 6.75 17.5 6.75V5.25ZM7 5.25C6.58579 5.25 6.25 5.58579 6.25 6C6.25 6.41421 6.58579 6.75 7 6.75V5.25ZM9 19.25C8.58579 19.25 8.25 19.5858 8.25 20C8.25 20.4142 8.58579 20.75 9 20.75V19.25ZM15 20.75C15.4142 20.75 15.75 20.4142 15.75 20C15.75 19.5858 15.4142 19.25 15 19.25V20.75ZM10.25 11.25V20H11.75V11.25H10.25ZM11 19.25H4.23256V20.75H11V19.25ZM2.75 17.3953V11.25H1.25V17.3953H2.75ZM4.23256 19.25C3.51806 19.25 2.75 18.5323 2.75 17.3953H1.25C1.25 19.1354 2.48104 20.75 4.23256 20.75V19.25ZM6.5 6.75C8.46677 6.75 10.25 8.65209 10.25 11.25H11.75C11.75 8.04892 9.50379 5.25 6.5 5.25V6.75ZM6.5 5.25C3.49621 5.25 1.25 8.04892 1.25 11.25H2.75C2.75 8.65209 4.53323 6.75 6.5 6.75V5.25ZM21.25 11.25V17.4253H22.75V11.25H21.25ZM19.7931 19.25H14V20.75H19.7931V19.25ZM21.25 17.4253C21.25 18.5457 20.4934 19.25 19.7931 19.25V20.75C21.5305 20.75 22.75 19.1488 22.75 17.4253H21.25ZM22.75 11.25C22.75 8.04892 20.5038 5.25 17.5 5.25V6.75C19.4668 6.75 21.25 8.65209 21.25 11.25H22.75ZM7 6.75H18V5.25H7V6.75ZM9 20.75H15V19.25H9V20.75Z" fill="#1C274C"/>
<path d="M5 16H8" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M16 9.88432V5.41121M16 5.41121V2.63519C16 2.39905 16.1676 2.19612 16.3994 2.15144L16.8855 2.05779C17.4738 1.94443 18.0821 1.99855 18.6412 2.214L18.7203 2.24451C19.2746 2.4581 19.8807 2.498 20.4582 2.35891C20.7343 2.2924 21 2.50168 21 2.78573V5.00723C21 5.2442 20.8376 5.45031 20.6073 5.5058L20.5407 5.52184C19.9095 5.67387 19.247 5.63026 18.6412 5.39679C18.0821 5.18135 17.4738 5.12722 16.8855 5.24058L16 5.41121Z" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Search",
      link: "/search",
      description: "Lighting fast site search, without a backend.",
      svgContent: `
<path d="M22 13V12C22 8.22876 22 6.34315 20.8284 5.17157C19.6569 4 17.7712 4 14 4H10C6.22876 4 4.34315 4 3.17157 5.17157C2 6.34315 2 8.22876 2 12C2 15.7712 2 17.6569 3.17157 18.8284C4.34315 20 6.22876 20 10 20H13" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M10 16H6" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M2 10L22 10" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<circle cx="18" cy="17" r="3" stroke="#1C274C" stroke-width="1.5"/>
<path d="M20.5 19.5L21.5 20.5" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Email",
      link: "https://github.com/CriticalMoments/CMSaasStarter/blob/main/email_docs.md",
      linkText: "Docs",
      description: "Send emails to users, including template support.",
      svgContent: `<path d="M22 10C22.0185 10.7271 22 11.0542 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H13" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M6 8L8.1589 9.79908C9.99553 11.3296 10.9139 12.0949 12 12.0949C13.0861 12.0949 14.0045 11.3296 15.8411 9.79908" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<circle cx="19" cy="5" r="3" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Performance",
      newPage: true,
      linkText: "Docs",
      link: "https://github.com/CriticalMoments/CMSaasStarter?tab=readme-ov-file#performance--best-practices",
      description:
        "Pre-rendering. Compiled on deployment to remove unused CSS/JS. Perfect 100/100 Google performance scores.",
      svgContent: `<path d="M5.66953 9.91436L8.73167 5.77133C10.711 3.09327 11.7007 1.75425 12.6241 2.03721C13.5474 2.32018 13.5474 3.96249 13.5474 7.24712V7.55682C13.5474 8.74151 13.5474 9.33386 13.926 9.70541L13.946 9.72466C14.3327 10.0884 14.9492 10.0884 16.1822 10.0884C18.4011 10.0884 19.5106 10.0884 19.8855 10.7613C19.8917 10.7724 19.8977 10.7837 19.9036 10.795C20.2576 11.4784 19.6152 12.3475 18.3304 14.0857L15.2683 18.2287C13.2889 20.9067 12.2992 22.2458 11.3758 21.9628C10.4525 21.6798 10.4525 20.0375 10.4525 16.7528L10.4526 16.4433C10.4526 15.2585 10.4526 14.6662 10.074 14.2946L10.054 14.2754C9.6673 13.9117 9.05079 13.9117 7.81775 13.9117C5.59888 13.9117 4.48945 13.9117 4.1145 13.2387C4.10829 13.2276 4.10225 13.2164 4.09639 13.205C3.74244 12.5217 4.3848 11.6526 5.66953 9.91436Z" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Responsive",
      description: "Designed for mobile and desktop.",
      svgContent: `<path d="M11 17H8C5.17157 17 3.75736 17 2.87868 16.1213C2 15.2426 2 13.8284 2 11V10C2 6.22876 2 4.34315 3.17157 3.17157C4.34315 2 6.22876 2 10 2H15.5C17.8346 2 19.0019 2 19.8856 2.47231C20.5833 2.84525 21.1548 3.4167 21.5277 4.11441C22 4.99805 22 6.16537 22 8.5" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M14 15C14 13.1144 14 12.1716 14.5858 11.5858C15.1716 11 16.1144 11 18 11C19.8856 11 20.8284 11 21.4142 11.5858C22 12.1716 22 13.1144 22 15V18C22 19.8856 22 20.8284 21.4142 21.4142C20.8284 22 19.8856 22 18 22C16.1144 22 15.1716 22 14.5858 21.4142C14 20.8284 14 19.8856 14 18V15Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M19 20H17" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11 22H8" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11 22V17" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
<path d="M11 13H2" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Delightful Stack",
      description:
        "Tools you'll love working with, including SvelteKit, Tailwind, DaisyUI, Postgres, and Supabase.",
      linkText: "Docs",
      newPage: true,
      link: "https://github.com/CriticalMoments/CMSaasStarter?tab=readme-ov-file#tech-stack",
      svgContent: `<path d="M4.97883 9.68508C2.99294 8.89073 2 8.49355 2 8C2 7.50645 2.99294 7.10927 4.97883 6.31492L7.7873 5.19153C9.77318 4.39718 10.7661 4 12 4C13.2339 4 14.2268 4.39718 16.2127 5.19153L19.0212 6.31492C21.0071 7.10927 22 7.50645 22 8C22 8.49355 21.0071 8.89073 19.0212 9.68508L16.2127 10.8085C14.2268 11.6028 13.2339 12 12 12C10.7661 12 9.77318 11.6028 7.7873 10.8085L4.97883 9.68508Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M5.76613 10L4.97883 10.3149C2.99294 11.1093 2 11.5065 2 12C2 12.4935 2.99294 12.8907 4.97883 13.6851L7.7873 14.8085C9.77318 15.6028 10.7661 16 12 16C13.2339 16 14.2268 15.6028 16.2127 14.8085L19.0212 13.6851C21.0071 12.8907 22 12.4935 22 12C22 11.5065 21.0071 11.1093 19.0212 10.3149L18.2339 10" stroke="#1C274C" stroke-width="1.5"/>
<path d="M5.76613 14L4.97883 14.3149C2.99294 15.1093 2 15.5065 2 16C2 16.4935 2.99294 16.8907 4.97883 17.6851L7.7873 18.8085C9.77318 19.6028 10.7661 20 12 20C13.2339 20 14.2268 19.6028 16.2127 18.8085L19.0212 17.6851C21.0071 16.8907 22 16.4935 22 16C22 15.5065 21.0071 15.1093 19.0212 14.3149L18.2339 14" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Extensible",
      description:
        "All the tools you need to make additional marketing pages, UI components, admin portals, database backends, API endpoints, and more.",
      svgContent: `<path d="M12.75 6.5C12.75 6.08579 12.4142 5.75 12 5.75C11.5858 5.75 11.25 6.08579 11.25 6.5H12.75ZM18 16.5L18.5303 17.0303C18.8232 16.7374 18.8232 16.2626 18.5303 15.9697L18 16.5ZM15.9697 17.4697C15.6768 17.7626 15.6768 18.2374 15.9697 18.5303C16.2626 18.8232 16.7374 18.8232 17.0303 18.5303L15.9697 17.4697ZM17.0303 14.4697C16.7374 14.1768 16.2626 14.1768 15.9697 14.4697C15.6768 14.7626 15.6768 15.2374 15.9697 15.5303L17.0303 14.4697ZM11.25 6.5V12.5H12.75V6.5H11.25ZM16 17.25H18V15.75H16V17.25ZM17.4697 15.9697L15.9697 17.4697L17.0303 18.5303L18.5303 17.0303L17.4697 15.9697ZM18.5303 15.9697L17.0303 14.4697L15.9697 15.5303L17.4697 17.0303L18.5303 15.9697ZM11.25 12.5C11.25 15.1234 13.3766 17.25 16 17.25V15.75C14.2051 15.75 12.75 14.2949 12.75 12.5H11.25Z" fill="#1C274C"/>
<path d="M12.75 6.5C12.75 6.08579 12.4142 5.75 12 5.75C11.5858 5.75 11.25 6.08579 11.25 6.5H12.75ZM6 16.5L5.46967 15.9697C5.17678 16.2626 5.17678 16.7374 5.46967 17.0303L6 16.5ZM6.96967 18.5303C7.26256 18.8232 7.73744 18.8232 8.03033 18.5303C8.32322 18.2374 8.32322 17.7626 8.03033 17.4697L6.96967 18.5303ZM8.03033 15.5303C8.32322 15.2374 8.32322 14.7626 8.03033 14.4697C7.73744 14.1768 7.26256 14.1768 6.96967 14.4697L8.03033 15.5303ZM11.25 6.5V12.5H12.75V6.5H11.25ZM8 15.75H6V17.25H8V15.75ZM5.46967 17.0303L6.96967 18.5303L8.03033 17.4697L6.53033 15.9697L5.46967 17.0303ZM6.53033 17.0303L8.03033 15.5303L6.96967 14.4697L5.46967 15.9697L6.53033 17.0303ZM11.25 12.5C11.25 14.2949 9.79493 15.75 8 15.75V17.25C10.6234 17.25 12.75 15.1234 12.75 12.5H11.25Z" fill="#1C274C"/>
<path d="M22 12C22 16.714 22 19.0711 20.5355 20.5355C19.0711 22 16.714 22 12 22C7.28595 22 4.92893 22 3.46447 20.5355C2 19.0711 2 16.714 2 12C2 7.28595 2 4.92893 3.46447 3.46447C4.92893 2 7.28595 2 12 2C16.714 2 19.0711 2 20.5355 3.46447C21.5093 4.43821 21.8356 5.80655 21.9449 8" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>
`,
    },
    {
      name: "Developer Experience",
      description:
        "Built in linting, type checking, formatting, and test framework. Run locally in your editor, and on Github CI.",
      newPage: true,
      linkText: "Docs",
      link: "https://github.com/CriticalMoments/CMSaasStarter?tab=readme-ov-file#developer-tools",
      svgContent: `<path d="M7 9C7 9.55228 6.55228 10 6 10C5.44772 10 5 9.55228 5 9C5 8.44772 5.44772 8 6 8C6.55228 8 7 8.44772 7 9Z" fill="#1C274C"/>
<path d="M7 12C7 12.5523 6.55228 13 6 13C5.44772 13 5 12.5523 5 12C5 11.4477 5.44772 11 6 11C6.55228 11 7 11.4477 7 12Z" fill="#1C274C"/>
<path d="M10 12C10 12.5523 9.55228 13 9 13C8.44772 13 8 12.5523 8 12C8 11.4477 8.44772 11 9 11C9.55228 11 10 11.4477 10 12Z" fill="#1C274C"/>
<path d="M10 9C10 9.55228 9.55228 10 9 10C8.44772 10 8 9.55228 8 9C8 8.44772 8.44772 8 9 8C9.55228 8 10 8.44772 10 9Z" fill="#1C274C"/>
<path d="M13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8C12.5523 8 13 8.44772 13 9Z" fill="#1C274C"/>
<path d="M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z" fill="#1C274C"/>
<path d="M16 9C16 9.55228 15.5523 10 15 10C14.4477 10 14 9.55228 14 9C14 8.44772 14.4477 8 15 8C15.5523 8 16 8.44772 16 9Z" fill="#1C274C"/>
<path d="M16 12C16 12.5523 15.5523 13 15 13C14.4477 13 14 12.5523 14 12C14 11.4477 14.4477 11 15 11C15.5523 11 16 11.4477 16 12Z" fill="#1C274C"/>
<path d="M19 9C19 9.55228 18.5523 10 18 10C17.4477 10 17 9.55228 17 9C17 8.44772 17.4477 8 18 8C18.5523 8 19 8.44772 19 9Z" fill="#1C274C"/>
<path d="M19 12C19 12.5523 18.5523 13 18 13C17.4477 13 17 12.5523 17 12C17 11.4477 17.4477 11 18 11C18.5523 11 19 11.4477 19 12Z" fill="#1C274C"/>
<path d="M2 11C2 8.17157 2 6.75736 2.87868 5.87868C3.75736 5 5.17157 5 8 5H16C18.8284 5 20.2426 5 21.1213 5.87868C22 6.75736 22 8.17157 22 11V13C22 15.8284 22 17.2426 21.1213 18.1213C20.2426 19 18.8284 19 16 19H8C5.17157 19 3.75736 19 2.87868 18.1213C2 17.2426 2 15.8284 2 13V11Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M7 16H17" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round"/>`,
    },
    {
      name: "Scalable",
      description:
        "Handle new users and scale with horizontally scaling edge functions.",
      svgContent: `<path d="M3 22H21" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3 11C3 10.0572 3 9.58579 3.29289 9.29289C3.58579 9 4.05719 9 5 9C5.94281 9 6.41421 9 6.70711 9.29289C7 9.58579 7 10.0572 7 11V17C7 17.9428 7 18.4142 6.70711 18.7071C6.41421 19 5.94281 19 5 19C4.05719 19 3.58579 19 3.29289 18.7071C3 18.4142 3 17.9428 3 17V11Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M10 7C10 6.05719 10 5.58579 10.2929 5.29289C10.5858 5 11.0572 5 12 5C12.9428 5 13.4142 5 13.7071 5.29289C14 5.58579 14 6.05719 14 7V17C14 17.9428 14 18.4142 13.7071 18.7071C13.4142 19 12.9428 19 12 19C11.0572 19 10.5858 19 10.2929 18.7071C10 18.4142 10 17.9428 10 17V7Z" stroke="#1C274C" stroke-width="1.5"/>
<path d="M17 4C17 3.05719 17 2.58579 17.2929 2.29289C17.5858 2 18.0572 2 19 2C19.9428 2 20.4142 2 20.7071 2.29289C21 2.58579 21 3.05719 21 4V17C21 17.9428 21 18.4142 20.7071 18.7071C20.4142 19 19.9428 19 19 19C18.0572 19 17.5858 19 17.2929 18.7071C17 18.4142 17 17.9428 17 17V4Z" stroke="#1C274C" stroke-width="1.5"/>`,
    },
    {
      name: "Extensions",
      description:
        "Community extensions for internationalization, dark mode theme, and more.",
      linkText: "Docs",
      newPage: true,
      link: "https://github.com/CriticalMoments/CMSaasStarter?tab=readme-ov-file#community-extensions",
      svgContent: `
      <circle cx="10" cy="6" r="4" stroke="#1C274C" stroke-width="1.5"/>
<path d="M18 17.5C18 19.9853 18 22 10 22C2 22 2 19.9853 2 17.5C2 15.0147 5.58172 13 10 13C14.4183 13 18 15.0147 18 17.5Z" stroke="currentColor" stroke-width="1.5"/>
<path d="M18.0885 12.5385L18.5435 11.9423L18.0885 12.5385ZM19 8.64354L18.4681 9.17232C18.6089 9.31392 18.8003 9.39354 19 9.39354C19.1997 9.39354 19.3911 9.31392 19.5319 9.17232L19 8.64354ZM19.9115 12.5385L19.4565 11.9423L19.9115 12.5385ZM18.5435 11.9423C18.0571 11.571 17.619 11.274 17.2659 10.8891C16.9387 10.5324 16.75 10.1638 16.75 9.69973H15.25C15.25 10.6481 15.6642 11.362 16.1606 11.9031C16.6311 12.4161 17.2372 12.8322 17.6335 13.1347L18.5435 11.9423ZM16.75 9.69973C16.75 9.28775 16.9898 8.95469 17.2973 8.81862C17.5635 8.7008 17.9874 8.68874 18.4681 9.17232L19.5319 8.11476C18.6627 7.24047 17.5865 7.0503 16.6903 7.44694C15.8352 7.82533 15.25 8.69929 15.25 9.69973H16.75ZM17.6335 13.1347C17.7825 13.2483 17.9756 13.3959 18.1793 13.5111C18.3832 13.6265 18.6656 13.75 19 13.75V12.25C19.0344 12.25 19.0168 12.2615 18.9179 12.2056C18.8187 12.1495 18.7061 12.0663 18.5435 11.9423L17.6335 13.1347ZM20.3665 13.1347C20.7628 12.8322 21.3689 12.4161 21.8394 11.9031C22.3358 11.362 22.75 10.6481 22.75 9.69973H21.25C21.25 10.1638 21.0613 10.5324 20.7341 10.8891C20.381 11.274 19.9429 11.571 19.4565 11.9423L20.3665 13.1347ZM22.75 9.69973C22.75 8.69929 22.1648 7.82533 21.3097 7.44694C20.4135 7.0503 19.3373 7.24047 18.4681 8.11476L19.5319 9.17232C20.0126 8.68874 20.4365 8.7008 20.7027 8.81862C21.0102 8.95469 21.25 9.28775 21.25 9.69973H22.75ZM19.4565 11.9423C19.2939 12.0663 19.1813 12.1495 19.0821 12.2056C18.9832 12.2615 18.9656 12.25 19 12.25V13.75C19.3344 13.75 19.6168 13.6265 19.8207 13.5111C20.0244 13.3959 20.2175 13.2483 20.3665 13.1347L19.4565 11.9423Z" fill="currentColor"/>
      `,
    },
    {
      name: "Open Source",
      description:
        "With the MIT license, you are free to use, modify, and distribute this template.",
      linkText: "License",
      newPage: true,
      link: "https://github.com/CriticalMoments/CMSaasStarter/blob/main/LICENSE",
      svgContent: `
<path d="M0.90625 12.4866C1.01075 6.46069 5.22856 2.1707 10.3917 1.47632C16.551 0.652008 21.732 4.84163 22.7275 10.3994C23.6728 15.6704 20.7757 20.6046 15.9989 22.5599C15.5871 22.7276 15.3616 22.6348 15.2042 22.2161L12.8302 16.0499C12.6948 15.6876 12.7869 15.4621 13.1417 15.3019C14.2162 14.8145 14.8852 13.9984 15.0646 12.8269C15.3472 11.0119 14.0334 9.33651 12.2039 9.17838C10.5381 9.01682 9.03525 10.1773 8.76644 11.8273C8.53337 13.2924 9.25663 14.6564 10.6206 15.2758C11.0469 15.469 11.1328 15.6588 10.9678 16.0926L8.58287 22.3014C8.46806 22.6059 8.21781 22.7139 7.89537 22.5881C5.37844 21.6036 3.31181 19.7233 2.09494 17.3101C0.98875 15.1259 0.978437 13.4182 0.90625 12.4832V12.4866ZM1.83369 12.4014C1.85225 12.6949 1.86188 13.0346 1.89144 13.3824C2.16988 16.5724 4.08937 19.7487 7.70219 21.5066C7.84587 21.5706 7.8995 21.5424 7.95312 21.4069C8.61244 19.6696 9.27794 17.9337 9.94756 16.1964C10.0046 16.0534 9.97575 15.9853 9.84306 15.9028C8.43987 15.018 7.74137 13.7503 7.81975 12.0858C7.86306 11.1295 8.23225 10.288 8.87644 9.58607C10.2047 8.13613 12.2919 7.82057 14.0004 8.80576C15.4111 9.62182 16.1983 11.201 16.0051 12.8201C15.8436 14.1731 15.1499 15.2119 13.9749 15.9138C13.8567 15.9853 13.8209 16.0424 13.8753 16.1813C14.5483 17.922 15.2179 19.6621 15.88 21.4028C15.9343 21.5424 15.9879 21.5706 16.1275 21.5025C17.7067 20.7683 19.0205 19.7157 20.038 18.3125C21.5807 16.1709 22.2181 13.7894 21.9135 11.1687C21.3085 5.9547 16.6177 1.63651 10.5773 2.38176C5.87206 2.95857 1.91206 6.92957 1.833 12.4007L1.83369 12.4014Z" fill="currentColor" stroke="currentColor" stroke-width="0.5" stroke-linecap="round"/>
      `,
    },
  ]
</script>

<svelte:head>
  <title>{WebsiteName}</title>
  <meta name="description" content={WebsiteDescription} />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet"
  />
  <link
    href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet"
  />
  <link
    href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet"
  />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- eslint-disable-next-line svelte/no-at-html-tags -->
  {@html jsonldScript}
</svelte:head>

<!-- HERO -->
<header
  class="relative font-sans pt-16 pb-28 leading-relaxed text-gray-900 bg-white"
>
  <div
    class="max-w-6xl grid lg:grid-cols-2 gap-12 border-0 mr-auto ml-auto pr-6 pl-6"
  >
    <div class="space-y-8">
      <span
        class="inline-flex items-center gap-2 animate-fade text-xs rounded-full pt-1.5 pr-3 pb-1.5 pl-3 font-sans tracking-wide text-white bg-gray-900 font-light"
        style="animation-delay: 0.1s;"
      >
        Marketing with Agents
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4 stroke-2"
        >
          <path
            d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"
          ></path>
          <path d="M20 3v4"></path>
          <path d="M22 5h-4"></path>
          <path d="M4 17v2"></path>
          <path d="M5 18H3"></path>
        </svg>
      </span>
      <h1
        class="sm:text-5xl md:text-6xl animate-fade text-4xl font-semibold tracking-tight"
        style="animation-delay:.2s; font-family: 'Plus Jakarta Sans', sans-serif;"
      >
        <span class="font-light tracking-tight">Agentic Marketing Team</span>
      </h1>
      <p
        class="animate-fade text-lg text-gray-700 tracking-wide font-light"
        style="animation-delay: 0.3s;"
      >
        A fleet of AI-powered specialists that grow audiences, craft content,
        and launch campaigns while you focus on strategy.
      </p>
      <div
        class="flex flex-col sm:flex-row gap-4 pt-4 animate-fade"
        style="animation-delay:.4s"
      >
        <a
          href="/onboarding"
          class="inline-flex justify-center items-center gap-2 transition-colors text-sm rounded-md pt-3 pr-6 pb-3 pl-6 font-sans tracking-wide text-white hover:bg-green-700 bg-zinc-800 font-light"
        >
          Sign Up
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4"
          >
            <path d="M5 12h14"></path>
            <path d="m12 5 7 7-7 7"></path>
          </svg>
        </a>
        <a
          href="/login"
          class="inline-flex gap-2 transition-colors text-sm border rounded-md pt-3 pr-6 pb-3 pl-6 items-center justify-center font-sans tracking-wide border-gray-300 hover:border-gray-900 font-light"
        >
          Sign In
        </a>
      </div>
    </div>
    <div class="relative">
      <div
        class="absolute -inset-px rounded-xl ring-1 ring-inset pointer-events-none ring-gray-200"
      ></div>
    </div>
  </div>
</header>

<!-- APPROACH -->
<section class="bg-white">
  <div
    class="max-w-6xl grid md:grid-cols-2 gap-12 border-0 mr-auto ml-auto pt-12 pr-6 pb-24 pl-6"
  >
    <div class="space-y-6">
      <h2
        class="text-lg animate-fade font-sans tracking-wide font-light"
        style="animation-delay: 0.1s;"
      >
        Built for modern marketers
      </h2>
      <p
        class="animate-fade text-sm font-sans tracking-wide text-gray-700 font-light"
        style="animation-delay: 0.2s;"
      >
        Traditional marketing suites are bloated and slow. Our modular agents
        collaborate instantly—learning from your data, optimizing decisions, and
        continuously iterating so your brand stays ahead.
      </p>
    </div>
    <ul class="space-y-8 text-sm">
      <li
        class="flex gap-4 items-start animate-fade"
        style="animation-delay:.3s"
      >
        <span class="inline-flex p-2 rounded-md bg-purple-50">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-5 h-5"
          >
            <path
              d="M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z"
            ></path>
          </svg>
        </span>
        <p class="font-sans tracking-wide font-light">
          <strong class="font-sans tracking-wide font-light"
            >Always-on Intelligence.</strong
          > Agents monitor market shifts 24/7 and adapt campaigns in real time.
        </p>
      </li>
      <li
        class="flex gap-4 items-start animate-fade"
        style="animation-delay:.4s"
      >
        <span class="inline-flex p-2 rounded-md bg-purple-50">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-5 h-5"
          >
            <rect width="18" height="7" x="3" y="3" rx="1"></rect>
            <rect width="9" height="7" x="3" y="14" rx="1"></rect>
            <rect width="5" height="7" x="16" y="14" rx="1"></rect>
          </svg>
        </span>
        <p class="font-sans tracking-wide font-light">
          <strong class="font-sans tracking-wide font-light"
            >Seamless Collaboration.</strong
          > Connect Slack, Notion, HubSpot & more. Agents slot into your workflow
          like teammates.
        </p>
      </li>
      <li
        class="flex gap-4 items-start animate-fade"
        style="animation-delay:.5s"
      >
        <span class="inline-flex p-2 rounded-md bg-purple-50">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-5 h-5"
          >
            <path d="M12 6v6l4 2"></path>
            <circle cx="12" cy="12" r="10"></circle>
          </svg>
        </span>
        <p class="font-sans tracking-wide font-light">
          <strong class="font-sans tracking-wide font-light"
            >Time back in your day.</strong
          > Reclaim hours of manual work each week and reinvest in strategy and creativity.
        </p>
      </li>
    </ul>
  </div>
</section>

<!-- AGENTS -->
<section id="agents" class="border-t border-gray-200 bg-white">
  <div class="max-w-6xl mx-auto px-6 py-24">
    <h2
      class="text-lg mb-12 animate-fade font-sans tracking-wide font-light"
      style="animation-delay: 0.1s;"
    >
      Your autonomous agent lineup
    </h2>
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
      {#each features.slice(0, 3) as feature, index}
        <div
          class="flex flex-col p-6 border rounded-lg hover:shadow transition-shadow animate-fade border-gray-200"
          style="animation-delay: {0.2 + index * 0.1}s;"
        >
          <div class="flex items-center justify-between mb-4">
            <div
              class="inline-flex items-center gap-2 font-sans tracking-wide font-light"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="w-5 h-5 text-indigo-600"
              >
                <!-- eslint-disable-next-line svelte/no-at-html-tags -->
                {@html feature.svgContent}
              </svg>
              {feature.name}
            </div>
            <span
              class="text-[10px] rounded-full px-2 py-0.5 font-sans tracking-wide bg-gray-900 text-white font-light"
              >{feature.linkText}</span
            >
          </div>
          <p
            class="text-sm mb-6 font-sans tracking-wide text-gray-700 font-light"
          >
            {feature.description}
          </p>
          {#if feature.subFeatures}
            <ul class="text-xs space-y-1 text-gray-600">
              {#each feature.subFeatures as subFeature}
                <li class="flex gap-2 font-sans tracking-wide font-light">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="w-3 h-3 text-indigo-600"
                  >
                    <path d="M20 6 9 17l-5-5"></path>
                  </svg>
                  {subFeature}
                </li>
              {/each}
            </ul>
          {/if}
        </div>
      {/each}
    </div>
  </div>
</section>

<!-- INSIGHT CHART -->
<section class="border-t border-gray-200 bg-white">
  <div
    class="max-w-6xl grid lg:grid-cols-2 gap-16 mr-auto ml-auto pt-24 pr-6 pb-24 pl-6"
  >
    <div class="space-y-6">
      <h2
        class="text-lg animate-fade font-sans tracking-wide font-light"
        style="animation-delay: 0.1s;"
      >
        Proven performance, visualized
      </h2>
      <p
        class="text-sm animate-fade font-sans tracking-wide text-gray-700 font-light"
        style="animation-delay: 0.2s;"
      >
        Our users see rapid uplift in conversion and ROI after activating
        agents. The chart shows average weekly results across 150+ SaaS
        customers.
      </p>
      <ul class="text-sm space-y-3">
        <li
          class="flex items-center gap-2 animate-fade font-sans tracking-wide font-light"
          style="animation-delay: 0.3s;"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4 text-indigo-600"
          >
            <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
            <path d="m9 11 3 3L22 4"></path>
          </svg>
          39% higher lead-to-SQL rate
        </li>
        <li
          class="flex items-center gap-2 animate-fade font-sans tracking-wide font-light"
          style="animation-delay: 0.4s;"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4 text-indigo-600"
          >
            <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
            <path d="m9 11 3 3L22 4"></path>
          </svg>
          28% lower cost per acquisition
        </li>
        <li
          class="flex items-center gap-2 animate-fade font-sans tracking-wide font-light"
          style="animation-delay: 0.5s;"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="w-4 h-4 text-indigo-600"
          >
            <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
            <path d="m9 11 3 3L22 4"></path>
          </svg>
          4× faster content iteration cycles
        </li>
      </ul>
    </div>
    <div class="animate-blur" style="animation-delay:.3s">
      <div class="rounded-lg border p-6 border-gray-200">
        <div>
          <canvas bind:this={chartCanvas} class="w-full h-64"></canvas>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA -->
<section class="border-t border-gray-200 bg-white">
  <div class="max-w-3xl mx-auto px-6 py-24 text-center space-y-8">
    <h2
      class="text-3xl sm:text-4xl animate-fade font-light tracking-tight"
      style="animation-delay: 0.1s; font-family: 'Plus Jakarta Sans', sans-serif;"
    >
      Unlock autonomous growth today
    </h2>
    <p
      class="text-lg animate-fade font-sans tracking-wide text-gray-700 font-light"
      style="animation-delay: 0.2s;"
    >
      Your first agent spins up in under 5 minutes. No credit card required.
    </p>
    <div
      class="flex flex-col sm:flex-row justify-center gap-4 animate-fade"
      style="animation-delay:.3s"
    >
      <a
        href="/onboarding"
        class="inline-flex justify-center items-center gap-2 rounded-md text-sm px-6 py-3 transition-colors font-sans tracking-wide text-white hover:bg-green-700 bg-zinc-800 font-light"
      >
        Start free trial
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4"
        >
          <path d="M5 12h14"></path>
          <path d="m12 5 7 7-7 7"></path>
        </svg>
      </a>
      <a
        href="/contact_us"
        class="inline-flex justify-center items-center gap-2 rounded-md border text-sm px-6 py-3 transition-colors font-sans tracking-wide border-gray-300 hover:border-gray-900 font-light"
      >
        Talk to sales
      </a>
    </div>
  </div>
</section>

<!-- Keep the existing features for backward compatibility -->
<div class="min-h-[60vh] bg-gray-50">
  <div class="pt-20 pb-8 px-7">
    <div class="max-w-lg mx-auto text-center">
      <div class="text-3xl md:text-5xl font-bold text-primary">
        All Features
      </div>
      <div class="mt-6 text-xl font-bold">
        And try them on this
        <span
          class="underline decoration-secondary decoration-[3px] md:decoration-[4px]"
        >
          fully functional demo
        </span>
      </div>
    </div>

    <div
      class="flex gap-6 mt-12 max-w-[1064px] mx-auto place-content-center flex-wrap"
    >
      {#each features as feature}
        <Card.Root
          class="flex items-center w-[270px] min-h-[300px] shadow-lg text-center"
        >
          <Card.Content class="grid justify-items-center">
            <div>
              <svg
                width="50px"
                height="50px"
                class="mb-2 mt-1"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <!-- eslint-disable-next-line svelte/no-at-html-tags -->
                {@html feature.svgContent}
              </svg>
            </div>
            <h2 class="text-xl font-bold">
              {feature.name}
            </h2>
            <p class="text-sm mb-3">
              {feature.description}
            </p>
            {#if feature.link}
              <a
                href={feature.link}
                class="{buttonVariants({
                  variant: 'outline',
                  size: 'sm',
                })} min-w-[100px]"
                target={feature.newPage ? "_blank" : ""}
              >
                {feature.linkText ? feature.linkText : "Try It"}
              </a>
            {/if}
          </Card.Content>
        </Card.Root>
      {/each}
    </div>
  </div>
</div>

<style>
  @keyframes fade-in {
    0% {
      opacity: 0;
      transform: translateY(24px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes blur-in {
    0% {
      filter: blur(20px);
      opacity: 0;
    }
    100% {
      filter: blur(0);
      opacity: 1;
    }
  }

  .animate-fade {
    animation: fade-in 0.9s forwards;
  }

  .animate-blur {
    animation: blur-in 1.2s forwards;
  }
</style>
